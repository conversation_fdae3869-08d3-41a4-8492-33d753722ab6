// Type definitions for @jest/globals
declare module '@jest/globals' {
  export const describe: jest.Describe;
  export const it: jest.It;
  export const test: jest.It;
  export const beforeAll: jest.Lifecycle;
  export const beforeEach: jest.Lifecycle;
  export const afterAll: jest.Lifecycle;
  export const afterEach: jest.Lifecycle;

  // Extended Expect interface with rejects and resolves
  export interface Expect extends jest.Expect {
    <T = unknown>(actual: T): jest.Matchers<void, T> & {
      rejects: jest.Matchers<Promise<void>, T>;
      resolves: jest.Matchers<Promise<void>, T>;
    };
  }

  export const expect: Expect;
  export const jest: jest.Jest;
}

// Type definitions for auth-helpers-nextjs
declare module '@supabase/auth-helpers-nextjs' {
  import { SupabaseClient } from '@supabase/supabase-js';
  import { NextRequest, NextResponse } from 'next/server';

  export function createRouteHandlerClient<T = unknown>(
    options: {
      cookies: () => unknown;
    },
    supabaseUrl?: string,
    supabaseKey?: string
  ): SupabaseClient<T>;

  export function createServerComponentClient<T = unknown>(
    options: {
      cookies: () => unknown;
    },
    supabaseUrl?: string,
    supabaseKey?: string
  ): SupabaseClient<T>;

  export function createMiddlewareClient<T = unknown>(
    req: NextRequest,
    res: NextResponse,
    supabaseUrl?: string,
    supabaseKey?: string
  ): SupabaseClient<T>;
}

// Type definitions for auth-helper
declare module '@/lib/auth' {
  import { NextRequest, NextResponse } from 'next/server';
  import { SupabaseClient } from '@supabase/supabase-js';

  export function isAuthenticated(
    req: NextRequest,
    res: NextResponse,
    supabase: SupabaseClient
  ): Promise<{ authenticated: boolean; user?: unknown }>;

  export function hasRole(
    req: NextRequest,
    res: NextResponse,
    supabase: SupabaseClient,
    roles: string[]
  ): Promise<{ hasRole: boolean; user?: unknown }>;
}

// NOTE: Pinecone client types removed - now using laws-API service

// Type definitions for neo4j client
declare module '@/lib/neo4j/client' {
  export function logActivityToNeo4j(activity: unknown): Promise<void>;
  export function getDriver(): unknown;
}

export {};
