/**
 * Configuration constants for the PI Lawyer AI application
 */

// Supabase configuration
export const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
export const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// API configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
export const LAWS_API_BASE_URL = process.env.NEXT_PUBLIC_LAWS_API_BASE_URL || 'https://legal-api-stg-gfunh6mfpa-uc.a.run.app';

// Feature flags
export const ENABLE_INSIGHTS = process.env.NEXT_PUBLIC_ENABLE_INSIGHTS !== 'false';
export const ENABLE_CHAT = process.env.NEXT_PUBLIC_ENABLE_CHAT !== 'false';

// Limits
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
